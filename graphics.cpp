// graphics.cpp - WinBGIm implementation
#include "graphics.h"
#include <conio.h>

// Global variables
HDC hdc = NULL;
HWND hwnd = NULL;
HINSTANCE hInstance = NULL;
int current_color = WHITE;
int bk_color = BLACK;

COLORREF colors[16] = {
    RGB(0, 0, 0),       // BLACK
    RGB(0, 0, 255),     // BLUE
    RGB(0, 255, 0),     // GREEN
    RGB(0, 255, 255),   // CYAN
    RGB(255, 0, 0),     // RED
    RGB(255, 0, 255),   // MAGENTA
    RGB(165, 42, 42),   // BROWN
    RGB(192, 192, 192), // LIGHTGRAY
    RGB(128, 128, 128), // DARKGRAY
    RGB(173, 216, 230), // LIGHTBLUE
    RGB(144, 238, 144), // LIGHTGREEN
    RGB(224, 255, 255), // LIGHTCYAN
    RGB(255, 182, 193), // LIGHTRED
    RGB(255, 182, 255), // LIGHTMAGENTA
    RGB(255, 255, 0),   // YELLOW
    RGB(255, 255, 255)  // WHITE
};

// Window procedure
LRESULT CALLBACK WndProc(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam) {
    switch(msg) {
        case WM_CLOSE:
            DestroyWindow(hwnd);
            break;
        case WM_DESTROY:
            PostQuitMessage(0);
            break;
        case WM_KEYDOWN:
            if(wParam == VK_ESCAPE) {
                PostQuitMessage(0);
            }
            break;
        default:
            return DefWindowProc(hwnd, msg, wParam, lParam);
    }
    return 0;
}

void initgraph(int *gd, int *gm, char *path) {
    const char* className = "BGIWindow";
    WNDCLASSEX wc;
    
    hInstance = GetModuleHandle(NULL);
    
    wc.cbSize = sizeof(WNDCLASSEX);
    wc.style = 0;
    wc.lpfnWndProc = WndProc;
    wc.cbClsExtra = 0;
    wc.cbWndExtra = 0;
    wc.hInstance = hInstance;
    wc.hIcon = LoadIcon(NULL, IDI_APPLICATION);
    wc.hCursor = LoadCursor(NULL, IDC_ARROW);
    wc.hbrBackground = (HBRUSH)(COLOR_WINDOW+1);
    wc.lpszMenuName = NULL;
    wc.lpszClassName = className;
    wc.hIconSm = LoadIcon(NULL, IDI_APPLICATION);
    
    if(!RegisterClassEx(&wc)) {
        MessageBox(NULL, "Window Registration Failed!", "Error!", MB_ICONEXCLAMATION | MB_OK);
        return;
    }
    
    hwnd = CreateWindowEx(
        WS_EX_CLIENTEDGE,
        className,
        "Graphics Window",
        WS_OVERLAPPEDWINDOW,
        CW_USEDEFAULT, CW_USEDEFAULT, 800, 600,
        NULL, NULL, hInstance, NULL);
    
    if(hwnd == NULL) {
        MessageBox(NULL, "Window Creation Failed!", "Error!", MB_ICONEXCLAMATION | MB_OK);
        return;
    }
    
    ShowWindow(hwnd, SW_SHOWNORMAL);
    UpdateWindow(hwnd);
    
    hdc = GetDC(hwnd);
    SetBkColor(hdc, colors[bk_color]);
}

void closegraph() {
    if(hdc) ReleaseDC(hwnd, hdc);
    if(hwnd) DestroyWindow(hwnd);
    UnregisterClass("BGIWindow", hInstance);
}

void putpixel(int x, int y, int color) {
    if(hdc) {
        SetPixel(hdc, x, y, colors[color]);
    }
}

int getpixel(int x, int y) {
    if(hdc) {
        COLORREF pixel = GetPixel(hdc, x, y);
        for(int i = 0; i < 16; i++) {
            if(colors[i] == pixel) return i;
        }
    }
    return 0;
}

void line(int x1, int y1, int x2, int y2) {
    if(hdc) {
        HPEN pen = CreatePen(PS_SOLID, 1, colors[current_color]);
        HPEN oldPen = (HPEN)SelectObject(hdc, pen);
        MoveToEx(hdc, x1, y1, NULL);
        LineTo(hdc, x2, y2);
        SelectObject(hdc, oldPen);
        DeleteObject(pen);
    }
}

void setcolor(int color) {
    current_color = color;
}

void setbkcolor(int color) {
    bk_color = color;
    if(hdc) SetBkColor(hdc, colors[color]);
}

void cleardevice() {
    if(hdc && hwnd) {
        RECT rect;
        GetClientRect(hwnd, &rect);
        HBRUSH brush = CreateSolidBrush(colors[bk_color]);
        FillRect(hdc, &rect, brush);
        DeleteObject(brush);
    }
}

int graphics_getch() {
    MSG msg;
    while(GetMessage(&msg, NULL, 0, 0) > 0) {
        if(msg.message == WM_KEYDOWN) {
            TranslateMessage(&msg);
            DispatchMessage(&msg);
            return (int)msg.wParam;
        }
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }
    return 0;
}

void delay(unsigned int milliseconds) {
    Sleep(milliseconds);
}
