// graphics.h for WinBGIm
// This is a simplified version for basic BGI functionality

#ifndef GRAPHICS_H
#define GRAPHICS_H

#include <windows.h>
#include <windowsx.h>
#include <iostream>
#include <sstream>
#include <cmath>

// BGI Constants
#define DETECT 0
#define VGA 9
#define VGAHI 2

// Colors
#define BLACK 0
#define BLUE 1
#define GREEN 2
#define CYAN 3
#define RED 4
#define MAGENTA 5
#define BROWN 6
#define LIGHTGRAY 7
#define DARKGRAY 8
#define LIGHTBLUE 9
#define LIGHTGREEN 10
#define LIGHTCYAN 11
#define LIGHTRED 12
#define LIGHTMAGENTA 13
#define YELLOW 14
#define WHITE 15

// Function declarations
void initgraph(int *gd, int *gm, char *path);
void closegraph();
void putpixel(int x, int y, int color);
int getpixel(int x, int y);
void line(int x1, int y1, int x2, int y2);
void circle(int x, int y, int radius);
void rectangle(int left, int top, int right, int bottom);
void setcolor(int color);
void setbkcolor(int color);
void cleardevice();
void outtextxy(int x, int y, char *textstring);
int getch();
void delay(unsigned int milliseconds);

// Global variables
extern HDC hdc;
extern HWND hwnd;
extern COLORREF colors[16];
extern int current_color;
extern int bk_color;

#endif // GRAPHICS_H
