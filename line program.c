#include <graphics.h>
#include <stdio.h>
#include <stdlib.h>

int main() {
    int x1, y1, x2, y2;
    int dx, dy, x, y, p, const1, const2, x_end;
    int gd = DETECT, gm;

    // Initialize graphics (path left empty for modern WinBGIm setup)
    initgraph(&gd, &gm, "");

    // Input coordinates
    printf("Enter the end point coordinates of the line (x1 y1 x2 y2):\n");
    scanf("%d %d %d %d", &x1, &y1, &x2, &y2);

    // Calculate differences
    dx = abs(x2 - x1);
    dy = abs(y2 - y1);

    // Only works for slope between 0 and 1 (dx > dy)
    if (dx > dy) {
        p = 2 * dy - dx;
        const1 = 2 * dy;
        const2 = 2 * (dy - dx);

        if (x1 > x2) {
            x = x2;
            y = y2;
            x_end = x1;
        } else {
            x = x1;
            y = y1;
            x_end = x2;
        }

        putpixel(x, y, WHITE);

        while (x < x_end) {
            x++;
            if (p < 0) {
                p += const1;
            } else {
                y += (y2 > y1) ? 1 : -1;
                p += const2;
            }
            putpixel(x, y, WHITE);
        }
    } else {
        // For steep slope (dy >= dx)
        p = 2 * dx - dy;
        const1 = 2 * dx;
        const2 = 2 * (dx - dy);

        if (y1 > y2) {
            x = x2;
            y = y2;
            x_end = y1;
        } else {
            x = x1;
            y = y1;
            x_end = y2;
        }

        putpixel(x, y, WHITE);

        while (y < x_end) {
            y++;
            if (p < 0) {
                p += const1;
            } else {
                x += (x2 > x1) ? 1 : -1;
                p += const2;
            }
            putpixel(x, y, WHITE);
        }
    }

    getch();        // Pause window
    closegraph();   // Close graphics
    return 0;
}
