#include <stdio.h>
#include <stdlib.h>
#include <math.h>

#define GRID_WIDTH 50
#define GRID_HEIGHT 25

// Simple console grid to visualize the line
char grid[GRID_HEIGHT][GRID_WIDTH];

void initGrid() {
    for (int i = 0; i < GRID_HEIGHT; i++) {
        for (int j = 0; j < GRID_WIDTH; j++) {
            grid[i][j] = '.';
        }
    }
}

void putPixel(int x, int y) {
    // Scale coordinates to fit in our console grid
    int gridX = (x * (GRID_WIDTH - 1)) / 100;
    int gridY = (y * (GRID_HEIGHT - 1)) / 100;

    if (gridX >= 0 && gridX < GRID_WIDTH && gridY >= 0 && gridY < GRID_HEIGHT) {
        grid[gridY][gridX] = '*';
    }
}

void displayGrid() {
    printf("\nBresenham's Line Drawing Result:\n");
    printf("(Coordinates scaled to fit %dx%d console grid)\n\n", GRID_WIDTH, GRID_HEIGHT);

    for (int i = 0; i < GRID_HEIGHT; i++) {
        for (int j = 0; j < GRID_WIDTH; j++) {
            printf("%c", grid[i][j]);
        }
        printf("\n");
    }
}

int main() {
    int x1, y1, x2, y2;
    int dx, dy, x, y, p, const1, const2, x_end;

    // Initialize our console grid
    initGrid();

    // Input coordinates (0-100 range for better visualization)
    printf("Enter the end point coordinates of the line (x1 y1 x2 y2):\n");
    printf("(Use coordinates between 0-100 for best visualization)\n");
    scanf("%d %d %d %d", &x1, &y1, &x2, &y2);

    // Calculate differences
    dx = abs(x2 - x1);
    dy = abs(y2 - y1);

    // Only works for slope between 0 and 1 (dx > dy)
    if (dx > dy) {
        p = 2 * dy - dx;
        const1 = 2 * dy;
        const2 = 2 * (dy - dx);

        if (x1 > x2) {
            x = x2;
            y = y2;
            x_end = x1;
        } else {
            x = x1;
            y = y1;
            x_end = x2;
        }

        putPixel(x, y);

        while (x < x_end) {
            x++;
            if (p < 0) {
                p += const1;
            } else {
                y += (y2 > y1) ? 1 : -1;
                p += const2;
            }
            putPixel(x, y);
        }
    } else {
        // For steep slope (dy >= dx)
        p = 2 * dx - dy;
        const1 = 2 * dx;
        const2 = 2 * (dx - dy);

        if (y1 > y2) {
            x = x2;
            y = y2;
            x_end = y1;
        } else {
            x = x1;
            y = y1;
            x_end = y2;
        }

        putPixel(x, y);

        while (y < x_end) {
            y++;
            if (p < 0) {
                p += const1;
            } else {
                x += (x2 > x1) ? 1 : -1;
                p += const2;
            }
            putPixel(x, y);
        }
    }

    // Display the result
    displayGrid();

    printf("\nPress Enter to exit...");
    getchar(); // consume newline from scanf
    getchar(); // wait for user input
    return 0;
}
